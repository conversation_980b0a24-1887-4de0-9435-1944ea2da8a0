<template>
    <ut-page>
        <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
            <f-navbar fontColor="#fff" :bgColor="colors" title="校对异常处理" navbarType="1"></f-navbar>
        </ut-top>

        <view v-loading="loading" class="edit-container">
            <!-- 客户信息 -->
            <view class="cu-card margin-sm radius shadow bg-white flex padding-sm">
                <view class="flex-sub-0 margin-right-sm">
                    <u-lazy-load v-if="form.imgHead"
                        :image="$tools.showImg(form.imgHead)"
                        width="120"
                        height="160"
                        border-radius="4" />
                </view>
                <view class="flex-sub text-content">
                    <view class="flex justify-between align-center">
                        <view>
                            <view class="flex justify-start align-center">
                                <view class="text-df text-bold text-black">{{ form.name }}</view>
                                <view class="text-sm text-blue margin-left-lg" v-if="form.sex == 1">男
                                    <text class="cuIcon-male" />
                                </view>
                                <view class="text-sm text-pink margin-left-lg" v-if="form.sex == 2">女
                                    <text class="cuIcon-female" />
                                </view>
                            </view>
                            <view class="text-sm text-gray margin-top-xs">
                                <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                                {{ form.phone }}
                            </view>
                        </view>
                        <view class="text-right">
                            <view class="text-sm text-gray">护理日期</view>
                            <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                        </view>
                    </view>

                    <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                    <view v-if="form.attendantName" class="margin-top-xs">
                        <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                        <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                    </view>

                    <view v-if="form.idcard" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">证件号码：</text>
                        <text class="text-xs text-black">{{ form.idcard }}</text>
                    </view>

                    <!-- 额外信息 -->
                    <view v-if="form.schedulingDuration" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">排班时长：</text>
                        <text class="text-xs text-black">{{ form.schedulingDuration }}</text>
                    </view>

                    <view v-if="form.totalDuration" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">实际时长：</text>
                        <text class="text-xs text-black">{{ form.totalDuration }}</text>
                    </view>

                    <view v-if="form.checkInTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">签到时间：</text>
                        <text class="text-xs text-black">{{ form.checkInTime }}</text>
                    </view>

                    <view v-if="form.checkOutTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">签退时间：</text>
                        <text class="text-xs text-black">{{ form.checkOutTime }}</text>
                    </view>

                    <view v-if="form.lastManualUserName" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">校对人：</text>
                        <text class="text-xs text-black">{{ form.lastManualUserName }}</text>
                    </view>

                    <view v-if="form.lastManualTime" class="flex align-center margin-top-xs">
                        <text class="text-xs text-gray margin-right-xs">校对时间：</text>
                        <text class="text-xs text-black">{{ form.lastManualTime }}</text>
                    </view>

                    <view v-if="form.proofreadErrorRemark" class="margin-top-xs">
                        <text class="text-sm text-gray">校对异常信息：</text>
                        <text class="cu-tag sm bg-orange light radius">
                            {{ form.proofreadErrorRemark }}
                        </text>
                    </view>
                </view>
            </view>

            <!-- 服务项目 -->
            <view class="content-section">
                <view class="section-header">
                    <view class="section-title">服务项目</view>
                </view>
                <view v-if="form.projects && form.projects.length > 0" class="project-list">
                    <view v-for="(project, index) in form.projects" :key="index" class="project-item">
                        <view class="project-name">{{ project.name || project.projectName }}</view>
                        <view class="project-info">
                            <text class="project-detail">{{ project.description || '暂无描述' }}</text>
                            <view v-if="project.requireMinDuration || project.requireMaxDuration"
                                class="project-duration">
                                <text class="duration-text">
                                    时长要求：{{ project.requireMinDuration || 0 }}-{{
                                        project.requireMaxDuration || 0
                                    }}分钟
                                </text>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else class="empty-state">
                    <text>暂无服务项目</text>
                </view>
            </view>

            <!-- 服务资料 -->
            <view class="content-section">
                <view class="section-header">
                    <view class="section-title">服务资料</view>
                </view>
                <view v-if="datas && datas.length > 0" class="data-list">
                    <view v-for="(data, index) in datas" :key="index" class="data-item">
                        <view class="data-name">{{ data.name || data.title }}</view>
                        <view class="data-info">
                            <text class="data-detail">{{ data.description || '暂无描述' }}</text>
                            <view v-if="data.details && data.details.length > 0" class="data-details">
                                <view v-for="(detail, dIndex) in data.details" :key="dIndex" class="detail-item">
                                    <text class="detail-title">{{ detail.title }}</text>
                                    <text v-if="detail.require" class="detail-require">(必填)</text>
                                    <text v-else class="detail-optional">(选填)</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else class="empty-state">
                    <text>暂无服务资料</text>
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="footer-buttons">
                <u-button
                    type="primary"
                    text="确定"
                    @click="save"
                    :loading="loading"
                ></u-button>
                <u-button
                    text="返回"
                    @click="goBack"
                ></u-button>
            </view>

        </view>
    </ut-page>
</template>

<script>
let app = getApp();
import { mapState } from 'vuex'

export default {
    data() {
        return {
            colors: '',
            topWrapHeight: 0,
            workId: '',
            month: '',
            loading: false,
            form: {},
            datas: [],
        }
    },
    computed: {
        ...mapState({
            community: state => state.init.community,
        }),
    },
    onLoad(options) {
        this.workId = options.workId || ''
        this.month = options.month || ''
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
        this.getInfo()
    },
    methods: {
        getHeight(height, statusHeight) {
            this.topWrapHeight = height
        },
        async getInfo() {
            if (!this.workId) return
            this.loading = true
            const params = {
                communityId: this.community.id,
                workId: this.workId,
            }
            if (this.month) params.month = this.month
            try {
                const { data } = await this.$ut.api('mang/care/proofread/task', params)
                this.form = {
                    ...this.form,
                    ...data,
                    projects: data.projects?.map((item) => ({
                        ...item,
                        id: item.projectId,
                        _id: item.id,
                    })),
                }
                this.datas = data.datas || []
            } finally {
                this.loading = false
            }
        },
        async save() {
            this.loading = true
            try {
                await this.$ut.api('mang/care/proofread/errorSave', {
                    communityId: this.community.id,
                    workId: this.workId,
                    projectIds: this.form.projects?.map((item) => item.id),
                    datas: this.form.datas,
                })
                this.$u.toast('操作成功')
                this.goBack()
            } finally {
                this.loading = false
            }
        },

        goBack() {
            uni.navigateBack()
        },
    },
}
</script>

<style lang="scss" scoped>
.edit-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}



.content-section {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  padding: 30rpx 30rpx 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.project-list, .data-list {
  padding: 30rpx;

  .project-item, .data-item {
    padding: 25rpx;
    border: 1rpx solid #eee;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .project-name, .data-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
  }

  .project-detail, .data-detail {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }

  .project-duration {
    margin-top: 10rpx;

    .duration-text {
      font-size: 24rpx;
      color: #999;
    }
  }

  .data-details {
    margin-top: 15rpx;

    .detail-item {
      display: flex;
      align-items: center;
      margin-bottom: 8rpx;

      .detail-title {
        font-size: 24rpx;
        color: #666;
        margin-right: 10rpx;
      }

      .detail-require {
        font-size: 22rpx;
        color: #f56c6c;
      }

      .detail-optional {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80rpx 30rpx;
  color: #999;
  font-size: 28rpx;
}

.footer-buttons {
  background: white;
  padding: 30rpx;
  margin: 20rpx 0;
  border-radius: 12rpx;
  display: flex;
  gap: 20rpx;

  .u-button {
    flex: 1;
  }
}


</style>
